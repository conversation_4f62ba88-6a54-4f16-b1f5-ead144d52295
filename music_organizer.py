#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音乐文件整理和对比脚本
通过 Keyboard Maestro 触发
"""

import os
import shutil
import json
from pathlib import Path
from mutagen import File
from mutagen.id3 import ID3NoHeaderError
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('music_organizer.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class MusicOrganizer:
    def __init__(self):
        self.source_path = "/Volumes/music/虾米网易云/流行"
        self.base_path = "/Volumes/music/虾米网易云"
        self.target_album = "一人一首成名曲"
        self.target_folder = os.path.join(self.base_path, self.target_album)
        self.compare_paths = [
            "/Volumes/music/虾米网易云/流行",
            "/Volumes/music/虾米网易云/专辑集"
        ]
        
        # 支持的音乐文件格式
        self.supported_formats = {'.mp3', '.flac', '.m4a', '.wav', '.aac', '.ogg', '.wma'}
        
    def get_music_metadata(self, file_path):
        """获取音乐文件的元数据"""
        try:
            # 跳过系统隐藏文件
            file_name = os.path.basename(file_path)
            if file_name.startswith('._') or file_name.startswith('.DS_Store'):
                return None

            audio_file = File(file_path)
            if audio_file is None:
                return None

            metadata = {
                'title': '',
                'artist': '',
                'album': '',
                'file_path': file_path,
                'file_name': file_name
            }
            
            # 获取标题
            if 'TIT2' in audio_file:  # MP3
                metadata['title'] = str(audio_file['TIT2'])
            elif '\xa9nam' in audio_file:  # M4A
                metadata['title'] = str(audio_file['\xa9nam'][0])
            elif 'TITLE' in audio_file:  # FLAC/OGG
                metadata['title'] = str(audio_file['TITLE'][0])
                
            # 获取艺术家
            if 'TPE1' in audio_file:  # MP3
                metadata['artist'] = str(audio_file['TPE1'])
            elif '\xa9ART' in audio_file:  # M4A
                metadata['artist'] = str(audio_file['\xa9ART'][0])
            elif 'ARTIST' in audio_file:  # FLAC/OGG
                metadata['artist'] = str(audio_file['ARTIST'][0])
                
            # 获取专辑
            if 'TALB' in audio_file:  # MP3
                metadata['album'] = str(audio_file['TALB'])
            elif '\xa9alb' in audio_file:  # M4A
                metadata['album'] = str(audio_file['\xa9alb'][0])
            elif 'ALBUM' in audio_file:  # FLAC/OGG
                metadata['album'] = str(audio_file['ALBUM'][0])
                
            return metadata
            
        except Exception as e:
            logging.error(f"读取文件元数据失败: {file_path}, 错误: {e}")
            return None
    
    def find_target_album_files(self):
        """查找专辑为'一人一首成名曲'的音乐文件"""
        target_files = []
        
        if not os.path.exists(self.source_path):
            logging.error(f"源路径不存在: {self.source_path}")
            return target_files
            
        logging.info(f"开始扫描路径: {self.source_path}")
        
        for root, dirs, files in os.walk(self.source_path):
            for file in files:
                # 跳过系统隐藏文件
                if file.startswith('._') or file.startswith('.DS_Store'):
                    continue

                if Path(file).suffix.lower() in self.supported_formats:
                    file_path = os.path.join(root, file)
                    metadata = self.get_music_metadata(file_path)
                    
                    if metadata and metadata['album'] == self.target_album:
                        target_files.append(metadata)
                        logging.info(f"找到目标文件: {file}")
                        
        logging.info(f"共找到 {len(target_files)} 个目标文件")
        return target_files
    
    def create_target_folder_and_move_files(self, target_files):
        """创建目标文件夹并移动文件"""
        if not target_files:
            logging.info("没有找到需要移动的文件")
            return
            
        # 创建目标文件夹
        os.makedirs(self.target_folder, exist_ok=True)
        logging.info(f"创建/确认目标文件夹: {self.target_folder}")
        
        moved_files = []
        for file_info in target_files:
            source_file = file_info['file_path']
            target_file = os.path.join(self.target_folder, file_info['file_name'])
            
            try:
                # 如果目标文件已存在，添加序号
                counter = 1
                original_target = target_file
                while os.path.exists(target_file):
                    name, ext = os.path.splitext(original_target)
                    target_file = f"{name}_{counter}{ext}"
                    counter += 1
                
                shutil.move(source_file, target_file)
                file_info['new_path'] = target_file
                moved_files.append(file_info)
                logging.info(f"移动文件: {source_file} -> {target_file}")
                
            except Exception as e:
                logging.error(f"移动文件失败: {source_file}, 错误: {e}")
                
        return moved_files
    
    def scan_compare_paths(self):
        """扫描对比路径中的所有音乐文件"""
        all_music_files = {}
        
        for compare_path in self.compare_paths:
            if not os.path.exists(compare_path):
                logging.warning(f"对比路径不存在: {compare_path}")
                continue
                
            logging.info(f"扫描对比路径: {compare_path}")
            
            for root, dirs, files in os.walk(compare_path):
                for file in files:
                    # 跳过系统隐藏文件
                    if file.startswith('._') or file.startswith('.DS_Store'):
                        continue

                    if Path(file).suffix.lower() in self.supported_formats:
                        file_path = os.path.join(root, file)
                        metadata = self.get_music_metadata(file_path)
                        
                        if metadata and metadata['title']:
                            title = metadata['title'].strip().lower()
                            if title not in all_music_files:
                                all_music_files[title] = []
                            all_music_files[title].append(metadata)
                            
        logging.info(f"对比路径中共找到 {len(all_music_files)} 个不同的歌曲标题")
        return all_music_files
    
    def compare_and_generate_report(self, moved_files, all_music_files):
        """对比歌曲并生成报告"""
        report = {
            'generated_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'target_album': self.target_album,
            'total_target_songs': len(moved_files),
            'comparison_results': []
        }
        
        for file_info in moved_files:
            title = file_info['title'].strip().lower()
            result = {
                'target_song': {
                    'title': file_info['title'],
                    'artist': file_info['artist'],
                    'file_name': file_info['file_name'],
                    'path': file_info.get('new_path', file_info['file_path'])
                },
                'matches': []
            }
            
            if title in all_music_files:
                for match in all_music_files[title]:
                    # 排除自己
                    if match['file_path'] != file_info['file_path']:
                        result['matches'].append({
                            'title': match['title'],
                            'artist': match['artist'],
                            'album': match['album'],
                            'file_name': match['file_name'],
                            'path': match['file_path']
                        })
            
            if not result['matches']:
                result['status'] = '没有'
            else:
                result['status'] = f'找到 {len(result["matches"])} 个同名歌曲'
                
            report['comparison_results'].append(result)
            
        return report
    
    def save_report(self, report):
        """保存报告"""
        # 保存JSON格式
        json_file = os.path.join(self.base_path, f"{self.target_album}_对比报告.json")
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
            
        # 保存可读格式
        txt_file = os.path.join(self.base_path, f"{self.target_album}_对比报告.txt")
        with open(txt_file, 'w', encoding='utf-8') as f:
            f.write(f"音乐对比报告\n")
            f.write(f"生成时间: {report['generated_time']}\n")
            f.write(f"目标专辑: {report['target_album']}\n")
            f.write(f"总歌曲数: {report['total_target_songs']}\n")
            f.write("="*50 + "\n\n")
            
            for result in report['comparison_results']:
                f.write(f"歌曲: {result['target_song']['title']}\n")
                f.write(f"艺术家: {result['target_song']['artist']}\n")
                f.write(f"文件: {result['target_song']['file_name']}\n")
                f.write(f"路径: {result['target_song']['path']}\n")
                f.write(f"状态: {result['status']}\n")
                
                if result['matches']:
                    f.write("同名歌曲:\n")
                    for i, match in enumerate(result['matches'], 1):
                        f.write(f"  {i}. {match['title']} - {match['artist']}\n")
                        f.write(f"     专辑: {match['album']}\n")
                        f.write(f"     文件: {match['file_name']}\n")
                        f.write(f"     路径: {match['path']}\n")
                
                f.write("-"*30 + "\n\n")
                
        logging.info(f"报告已保存: {json_file} 和 {txt_file}")
        
    def run(self):
        """执行主流程"""
        try:
            logging.info("开始执行音乐整理任务")
            
            # 第一步：查找目标专辑文件
            target_files = self.find_target_album_files()
            
            # 移动文件到目标文件夹
            moved_files = self.create_target_folder_and_move_files(target_files)
            
            if not moved_files:
                logging.info("没有文件被移动，任务结束")
                return
                
            # 第二步：扫描对比路径
            all_music_files = self.scan_compare_paths()
            
            # 第三步：对比并生成报告
            report = self.compare_and_generate_report(moved_files, all_music_files)
            
            # 保存报告
            self.save_report(report)
            
            logging.info("任务完成!")
            
        except Exception as e:
            logging.error(f"执行过程中出现错误: {e}")
            raise

if __name__ == "__main__":
    organizer = MusicOrganizer()
    organizer.run()
