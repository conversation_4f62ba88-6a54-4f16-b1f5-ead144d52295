#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交互式音乐文件整理脚本
允许用户选择要处理的专辑
"""

import os
import sys
from pathlib import Path
from mutagen import File
from collections import Counter

def get_music_metadata(file_path):
    """获取音乐文件的元数据"""
    try:
        # 跳过系统隐藏文件
        file_name = os.path.basename(file_path)
        if file_name.startswith('._') or file_name.startswith('.DS_Store'):
            return None
            
        audio_file = File(file_path)
        if audio_file is None:
            return None
            
        metadata = {
            'title': '',
            'artist': '',
            'album': '',
            'file_path': file_path,
            'file_name': file_name
        }
        
        # 获取专辑
        if 'TALB' in audio_file:  # MP3
            metadata['album'] = str(audio_file['TALB'])
        elif '\xa9alb' in audio_file:  # M4A
            metadata['album'] = str(audio_file['\xa9alb'][0])
        elif 'ALBUM' in audio_file:  # FLAC/OGG
            metadata['album'] = str(audio_file['ALBUM'][0])
            
        # 获取标题
        if 'TIT2' in audio_file:  # MP3
            metadata['title'] = str(audio_file['TIT2'])
        elif '\xa9nam' in audio_file:  # M4A
            metadata['title'] = str(audio_file['\xa9nam'][0])
        elif 'TITLE' in audio_file:  # FLAC/OGG
            metadata['title'] = str(audio_file['TITLE'][0])
            
        # 获取艺术家
        if 'TPE1' in audio_file:  # MP3
            metadata['artist'] = str(audio_file['TPE1'])
        elif '\xa9ART' in audio_file:  # M4A
            metadata['artist'] = str(audio_file['\xa9ART'][0])
        elif 'ARTIST' in audio_file:  # FLAC/OGG
            metadata['artist'] = str(audio_file['ARTIST'][0])
            
        return metadata
        
    except Exception:
        return None

def scan_albums():
    """扫描并返回所有专辑"""
    source_path = "/Volumes/music/虾米网易云/流行"
    supported_formats = {'.mp3', '.flac', '.m4a', '.wav', '.aac', '.ogg', '.wma'}
    
    if not os.path.exists(source_path):
        print(f"路径不存在: {source_path}")
        return {}
        
    print(f"正在扫描路径: {source_path}")
    
    albums = Counter()
    
    for root, dirs, files in os.walk(source_path):
        for file in files:
            if file.startswith('._') or file.startswith('.DS_Store'):
                continue
                
            if Path(file).suffix.lower() in supported_formats:
                file_path = os.path.join(root, file)
                metadata = get_music_metadata(file_path)
                
                if metadata and metadata['album']:
                    album = metadata['album'].strip()
                    albums[album] += 1
    
    return albums

def main():
    print("=== 交互式音乐文件整理工具 ===\n")
    
    # 扫描专辑
    albums = scan_albums()
    
    if not albums:
        print("没有找到任何专辑信息")
        return
    
    print(f"\n找到 {len(albums)} 个专辑:")
    print("-" * 50)
    
    # 显示专辑列表
    album_list = list(albums.most_common())
    for i, (album, count) in enumerate(album_list, 1):
        print(f"{i:2d}. {album} ({count} 首)")
    
    print("-" * 50)
    
    # 让用户选择
    while True:
        try:
            choice = input(f"\n请选择要处理的专辑 (1-{len(album_list)}) 或输入 'q' 退出: ").strip()
            
            if choice.lower() == 'q':
                print("退出程序")
                return
                
            choice_num = int(choice)
            if 1 <= choice_num <= len(album_list):
                selected_album = album_list[choice_num - 1][0]
                selected_count = album_list[choice_num - 1][1]
                break
            else:
                print(f"请输入 1-{len(album_list)} 之间的数字")
                
        except ValueError:
            print("请输入有效的数字")
    
    print(f"\n您选择了专辑: '{selected_album}' ({selected_count} 首)")
    
    # 确认操作
    confirm = input(f"确认要将专辑 '{selected_album}' 的所有歌曲移动到新文件夹吗? (y/N): ").strip().lower()
    
    if confirm != 'y':
        print("操作已取消")
        return
    
    # 更新原始脚本中的目标专辑
    script_path = "/Users/<USER>/Documents/augment-projects/check/music_organizer.py"
    
    try:
        with open(script_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换目标专辑
        import re
        pattern = r'self\.target_album = "[^"]*"'
        replacement = f'self.target_album = "{selected_album}"'
        new_content = re.sub(pattern, replacement, content)
        
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
            
        print(f"\n已更新脚本，目标专辑设置为: '{selected_album}'")
        print("现在可以运行主脚本进行文件整理了")
        
        # 询问是否立即运行
        run_now = input("是否立即运行整理脚本? (y/N): ").strip().lower()
        
        if run_now == 'y':
            print("\n开始运行整理脚本...")
            os.system(f"/Library/Frameworks/Python.framework/Versions/3.13/bin/python3 {script_path}")
        else:
            print("您可以稍后通过 Keyboard Maestro 或直接运行脚本来执行整理操作")
            
    except Exception as e:
        print(f"更新脚本失败: {e}")

if __name__ == "__main__":
    main()
