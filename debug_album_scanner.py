#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试脚本：扫描音乐文件的专辑标签
"""

import os
from pathlib import Path
from mutagen import File
import logging
from collections import Counter

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def get_music_metadata_debug(file_path):
    """获取音乐文件的元数据（调试版本）"""
    try:
        # 跳过系统隐藏文件
        file_name = os.path.basename(file_path)
        if file_name.startswith('._') or file_name.startswith('.DS_Store'):
            return None
            
        audio_file = File(file_path)
        if audio_file is None:
            return None
            
        metadata = {
            'title': '',
            'artist': '',
            'album': '',
            'file_path': file_path,
            'file_name': file_name
        }
        
        # 获取标题
        if 'TIT2' in audio_file:  # MP3
            metadata['title'] = str(audio_file['TIT2'])
        elif '\xa9nam' in audio_file:  # M4A
            metadata['title'] = str(audio_file['\xa9nam'][0])
        elif 'TITLE' in audio_file:  # FLAC/OGG
            metadata['title'] = str(audio_file['TITLE'][0])
            
        # 获取艺术家
        if 'TPE1' in audio_file:  # MP3
            metadata['artist'] = str(audio_file['TPE1'])
        elif '\xa9ART' in audio_file:  # M4A
            metadata['artist'] = str(audio_file['\xa9ART'][0])
        elif 'ARTIST' in audio_file:  # FLAC/OGG
            metadata['artist'] = str(audio_file['ARTIST'][0])
            
        # 获取专辑
        if 'TALB' in audio_file:  # MP3
            metadata['album'] = str(audio_file['TALB'])
        elif '\xa9alb' in audio_file:  # M4A
            metadata['album'] = str(audio_file['\xa9alb'][0])
        elif 'ALBUM' in audio_file:  # FLAC/OGG
            metadata['album'] = str(audio_file['ALBUM'][0])
            
        return metadata
        
    except Exception as e:
        logging.debug(f"读取文件元数据失败: {file_path}, 错误: {e}")
        return None

def scan_albums():
    """扫描并统计所有专辑"""
    source_path = "/Volumes/music/虾米网易云/流行"
    supported_formats = {'.mp3', '.flac', '.m4a', '.wav', '.aac', '.ogg', '.wma'}
    
    if not os.path.exists(source_path):
        print(f"路径不存在: {source_path}")
        return
        
    print(f"开始扫描路径: {source_path}")
    
    albums = Counter()
    target_songs = []
    total_files = 0
    successful_reads = 0
    
    for root, dirs, files in os.walk(source_path):
        for file in files:
            # 跳过系统隐藏文件
            if file.startswith('._') or file.startswith('.DS_Store'):
                continue
                
            if Path(file).suffix.lower() in supported_formats:
                total_files += 1
                file_path = os.path.join(root, file)
                metadata = get_music_metadata_debug(file_path)
                
                if metadata and metadata['album']:
                    successful_reads += 1
                    album = metadata['album'].strip()
                    albums[album] += 1
                    
                    # 检查是否包含"成名曲"关键词
                    if '成名曲' in album or '一人一首' in album:
                        target_songs.append({
                            'file': metadata['file_name'],
                            'album': album,
                            'title': metadata['title'],
                            'artist': metadata['artist']
                        })
    
    print(f"\n扫描完成!")
    print(f"总文件数: {total_files}")
    print(f"成功读取: {successful_reads}")
    print(f"读取成功率: {successful_reads/total_files*100:.1f}%")
    
    print(f"\n找到 {len(albums)} 个不同的专辑:")
    
    # 显示最常见的专辑
    print("\n最常见的专辑 (前20个):")
    for album, count in albums.most_common(20):
        print(f"  {count:3d} 首 - {album}")
    
    # 显示包含"成名曲"的专辑
    print(f"\n包含'成名曲'或'一人一首'的歌曲 ({len(target_songs)} 首):")
    for song in target_songs:
        print(f"  专辑: {song['album']}")
        print(f"  歌曲: {song['title']} - {song['artist']}")
        print(f"  文件: {song['file']}")
        print()
    
    # 查找可能的目标专辑
    print("\n可能的目标专辑 (包含关键词):")
    for album, count in albums.items():
        if any(keyword in album for keyword in ['成名曲', '一人一首', '经典', '精选', '合集']):
            print(f"  {count:3d} 首 - {album}")

if __name__ == "__main__":
    scan_albums()
