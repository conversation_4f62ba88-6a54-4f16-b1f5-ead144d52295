# 音乐文件整理和对比工具

这个工具可以帮您自动整理音乐文件，并生成对比报告。

## 功能说明

1. **查找特定专辑的音乐文件**：在指定路径中查找专辑标签为"一人一首成名曲"的音乐文件
2. **自动移动文件**：将找到的文件移动到专门的文件夹中
3. **歌曲对比**：将移动的歌曲与其他路径中的歌曲进行对比
4. **生成报告**：生成详细的对比报告，显示哪些歌曲有同名版本

## 安装步骤

### 1. 安装依赖
```bash
chmod +x install_requirements.sh
./install_requirements.sh
```

### 2. 设置执行权限
```bash
chmod +x run_music_organizer.sh
chmod +x music_organizer.py
```

## 使用方法

### 方法1：直接运行
```bash
python3 music_organizer.py
```

### 方法2：通过 Keyboard Maestro 触发
1. 打开 Keyboard Maestro
2. 创建新的宏
3. 设置触发条件（如快捷键）
4. 添加"Execute a Shell Script"动作
5. 在脚本内容中输入：
   ```bash
   /path/to/your/script/run_music_organizer.sh
   ```
   （请将路径替换为实际的脚本路径）

## 配置说明

脚本中的路径配置：
- **源路径**：`/Volumes/music/虾米网易云/流行`
- **基础路径**：`/Volumes/music/虾米网易云`
- **目标专辑**：`一人一首成名曲`
- **对比路径**：
  - `/Volumes/music/虾米网易云/流行`
  - `/Volumes/music/虾米网易云/专辑集`

如需修改这些路径，请编辑 `music_organizer.py` 文件中的 `__init__` 方法。

## 支持的音乐格式

- MP3
- FLAC
- M4A
- WAV
- AAC
- OGG
- WMA

## 输出文件

执行完成后会生成以下文件：
1. **一人一首成名曲_对比报告.json**：JSON格式的详细报告
2. **一人一首成名曲_对比报告.txt**：可读格式的报告
3. **music_organizer.log**：执行日志

## 报告内容说明

对于每首歌曲，报告会显示：
- 歌曲基本信息（标题、艺术家、文件名、路径）
- 对比状态：
  - "没有"：表示在对比路径中没有找到同名歌曲
  - "找到 X 个同名歌曲"：表示找到了同名歌曲
- 同名歌曲列表（如果有）：包含每个同名歌曲的详细信息

## 注意事项

1. **备份重要文件**：脚本会移动文件，建议先备份重要数据
2. **路径检查**：确保所有路径都存在且可访问
3. **权限问题**：确保脚本有读写目标路径的权限
4. **重复文件**：如果目标文件夹中已存在同名文件，会自动添加序号

## 故障排除

1. **"路径不存在"错误**：检查路径是否正确，磁盘是否已挂载
2. **"权限被拒绝"错误**：检查文件和文件夹的读写权限
3. **"模块未找到"错误**：运行安装脚本安装依赖

## 自定义修改

如需修改脚本行为，可以编辑 `music_organizer.py` 中的以下部分：
- 路径配置：`__init__` 方法
- 支持格式：`supported_formats` 变量
- 元数据读取：`get_music_metadata` 方法
- 报告格式：`save_report` 方法
