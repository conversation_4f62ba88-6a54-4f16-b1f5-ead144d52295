#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速设置脚本 - 选择要处理的专辑
"""

import re

def update_target_album(album_name):
    """更新主脚本中的目标专辑"""
    script_path = "/Users/<USER>/Documents/augment-projects/check/music_organizer.py"
    
    try:
        with open(script_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换目标专辑
        pattern = r'self\.target_album = "[^"]*"'
        replacement = f'self.target_album = "{album_name}"'
        new_content = re.sub(pattern, replacement, content)
        
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
            
        print(f"✅ 已设置目标专辑为: '{album_name}'")
        return True
        
    except Exception as e:
        print(f"❌ 更新失败: {e}")
        return False

def main():
    print("=== 快速设置音乐整理脚本 ===\n")
    
    print("根据扫描结果，您的音乐库中有以下专辑：")
    print()
    
    albums = [
        ("流行", 471),
        ("中国好声音", 132),
        ("颓4年", 82),
        ("Beyond", 72),
        ("四大天王", 54),
        ("那年夏天初中段", 41),
        ("苏州河", 39),
        ("合唱", 32),
        ("左麟右李", 31),
        ("高一5班", 30),
        ("121314特别长", 28),
        ("张国荣", 26),
        ("塘厦始末", 26),
        ("周杰伦", 17),
        ("高考毕业季", 10),
        ("民谣", 10),
        ("陈奕迅", 9),
        ("2003年劲歌金曲", 8)
    ]
    
    print("推荐的专辑选择：")
    print("1. 流行 (471首) - 最大的专辑，包含各种流行歌曲")
    print("2. 中国好声音 (132首) - 经典翻唱歌曲")
    print("3. Beyond (72首) - Beyond乐队经典歌曲")
    print("4. 四大天王 (54首) - 四大天王经典歌曲")
    print("5. 周杰伦 (17首) - 周杰伦歌曲")
    print()
    
    # 快速选择
    print("快速选择：")
    print("a) 流行专辑 (推荐，歌曲最多)")
    print("b) 中国好声音")
    print("c) Beyond")
    print("d) 四大天王")
    print("e) 周杰伦")
    print("f) 自定义输入专辑名")
    print()
    
    choice = input("请选择 (a-f): ").strip().lower()
    
    album_map = {
        'a': '流行',
        'b': '中国好声音', 
        'c': 'Beyond',
        'd': '四大天王',
        'e': '周杰伦'
    }
    
    if choice in album_map:
        selected_album = album_map[choice]
    elif choice == 'f':
        selected_album = input("请输入专辑名称: ").strip()
    else:
        print("无效选择，使用默认专辑：流行")
        selected_album = "流行"
    
    print(f"\n您选择的专辑: {selected_album}")
    
    if update_target_album(selected_album):
        print("\n✅ 设置完成！")
        print("\n现在您可以：")
        print("1. 通过 Keyboard Maestro 运行脚本")
        print("2. 或直接运行：")
        print("   /Library/Frameworks/Python.framework/Versions/3.13/bin/python3 music_organizer.py")
        print()
        print("脚本将会：")
        print(f"- 查找专辑为 '{selected_album}' 的所有歌曲")
        print("- 创建 '一人一首成名曲' 文件夹")
        print("- 将这些歌曲移动到新文件夹")
        print("- 与其他路径的歌曲进行对比")
        print("- 生成详细报告")

if __name__ == "__main__":
    main()
