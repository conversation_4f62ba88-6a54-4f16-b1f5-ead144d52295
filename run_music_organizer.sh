#!/bin/bash
# Keyboard Maestro 触发脚本

# 使用完整的Python路径
PYTHON_PATH="/Library/Frameworks/Python.framework/Versions/3.13/bin/python3"

# 脚本的完整路径
SCRIPT_PATH="/Users/<USER>/Documents/augment-projects/check/music_organizer.py"

# 运行 Python 脚本
echo "开始执行音乐整理任务..."
$PYTHON_PATH $SCRIPT_PATH

# 检查执行结果
if [ $? -eq 0 ]; then
    echo "任务执行成功!"
    # 可以添加通知
    osascript -e 'display notification "音乐整理任务完成" with title "Music Organizer"'
else
    echo "任务执行失败!"
    osascript -e 'display notification "音乐整理任务失败，请检查日志" with title "Music Organizer"'
fi
