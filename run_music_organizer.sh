#!/bin/bash
# Keyboard Maestro 触发脚本

# 获取脚本所在目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

# 切换到脚本目录
cd "$SCRIPT_DIR"

# 运行 Python 脚本
echo "开始执行音乐整理任务..."
python3 music_organizer.py

# 检查执行结果
if [ $? -eq 0 ]; then
    echo "任务执行成功!"
    # 可以添加通知
    osascript -e 'display notification "音乐整理任务完成" with title "Music Organizer"'
else
    echo "任务执行失败!"
    osascript -e 'display notification "音乐整理任务失败，请检查日志" with title "Music Organizer"'
fi
